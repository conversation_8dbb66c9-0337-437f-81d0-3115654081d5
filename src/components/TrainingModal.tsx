import React, { useState } from 'react';
import { Modal, Platform, ScrollView, TouchableOpacity, FlatList } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Player } from '../models/player';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface TrainingModalProps {
  visible: boolean;
  players: Player[];
  onClose: () => void;
  onAssign: (player: Player, attribute: string) => void;
}

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-height: 80%;
  overflow: visible;
`;

const ModalTitle = styled(Text)<StyledProps>`
  font-size: 20px;
  font-family: 'NunitoBold';
  color: ${(props) => props.theme.colors.text.primary};
  text-align: center;
  margin-bottom: 20px;
`;

const SectionTitle = styled(Text)<StyledProps>`
  font-size: 16px;
  font-family: 'NunitoBold';
  color: ${(props) => props.theme.colors.text.primary};
  margin-bottom: 10px;
  margin-top: 10px;
`;

const DropdownContainer = styled.View<StyledProps>`
  margin-bottom: 16px;
  position: relative;
  z-index: 1000;
`;

const DropdownButton = styled(TouchableOpacity)<StyledProps>`
  padding: 12px 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background};
  border-width: 1px;
  border-color: ${(props) => props.theme.colors.text.secondary};
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

const DropdownText = styled(Text)<StyledProps>`
  font-size: 16px;
  font-family: 'Nunito';
  color: ${(props) => props.theme.colors.text.primary};
  flex: 1;
`;

const DropdownArrow = styled(Text)<StyledProps>`
  font-size: 16px;
  color: ${(props) => props.theme.colors.text.secondary};
  margin-left: 8px;
`;

const DropdownList = styled.View<StyledProps>`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 8px;
  border-width: 1px;
  border-color: ${(props) => props.theme.colors.text.secondary};
  max-height: 200px;
  z-index: 9999;
  shadow-color: #000;
  shadow-offset: 0px 4px;
  shadow-opacity: 0.25;
  shadow-radius: 4px;
  elevation: 5;
`;

const DropdownItem = styled(TouchableOpacity)<StyledProps>`
  padding: 12px 16px;
  border-bottom-width: 1px;
  border-bottom-color: ${(props) => props.theme.colors.background};
  background-color: ${(props) => props.theme.colors.surface};
`;

const DropdownItemText = styled(Text)<StyledProps>`
  font-size: 16px;
  font-family: 'Nunito';
  color: ${(props) => props.theme.colors.text.primary};
`;

const AssistantMessage = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.background};
  border-radius: 8px;
  padding: 12px;
  margin: 16px 0;
  border-left-width: 4px;
  border-left-color: ${(props) => props.theme.colors.primary};
`;

const AssistantMessageText = styled(Text)<StyledProps>`
  font-size: 14px;
  font-family: 'Nunito';
  color: ${(props) => props.theme.colors.text.primary};
  line-height: 20px;
  font-style: italic;
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin-top: 20px;
`;

const Button = styled(TouchableOpacity)<StyledProps>`
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.primary};
  margin: 0 5px;
`;

const ButtonText = styled(Text)<StyledProps>`
  color: ${(props) => props.theme.colors.surface};
  font-family: 'NunitoBold';
  text-align: center;
`;

const TrainingModal: React.FC<TrainingModalProps> = ({ visible, players, onClose, onAssign }) => {
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const [selectedAttribute, setSelectedAttribute] = useState<string | null>(null);
  const [showPlayerDropdown, setShowPlayerDropdown] = useState(false);
  const [showAttributeDropdown, setShowAttributeDropdown] = useState(false);

  const attributes = [
    { key: 'reflexes', name: 'Reflexes', category: 'Goalkeeper' },
    { key: 'positioning', name: 'Positioning', category: 'Goalkeeper' },
    { key: 'shotStopping', name: 'Shot Stopping', category: 'Goalkeeper' },
    { key: 'tackling', name: 'Tackling', category: 'Defender' },
    { key: 'marking', name: 'Marking', category: 'Defender' },
    { key: 'heading', name: 'Heading', category: 'Defender' },
    { key: 'finishing', name: 'Finishing', category: 'Attacker' },
    { key: 'pace', name: 'Pace', category: 'Attacker' },
    { key: 'crossing', name: 'Crossing', category: 'Attacker' },
    { key: 'passing', name: 'Passing', category: 'Midfielder' },
    { key: 'vision', name: 'Vision', category: 'Midfielder' },
    { key: 'ballControl', name: 'Ball Control', category: 'Midfielder' },
    { key: 'stamina', name: 'Stamina', category: 'General' },
  ];

  const getAssistantMessage = (player: Player): string => {
    const potentialText = player.trainingPotential > 0.8
      ? "has exceptional potential for improvement"
      : player.trainingPotential > 0.6
      ? "has good potential for development"
      : player.trainingPotential > 0.3
      ? "has moderate potential for growth"
      : player.trainingPotential > 0.1
      ? "has limited potential remaining"
      : "has reached their peak and may not improve much further";

    const topAreas = player.topTrainingAreas && player.topTrainingAreas.length > 0
      ? player.topTrainingAreas.slice(0, 3).join(', ')
      : "general fitness";

    return `Assistant Manager: "${player.firstName} ${potentialText}. I'd recommend focusing on ${topAreas} for the best results."`;
  };

  const handlePlayerSelect = (player: Player) => {
    setSelectedPlayer(player);
    setShowPlayerDropdown(false);
  };

  const handleAttributeSelect = (attribute: string) => {
    setSelectedAttribute(attribute);
    setShowAttributeDropdown(false);
  };

  const handleAssign = () => {
    if (selectedPlayer && selectedAttribute) {
      onAssign(selectedPlayer, selectedAttribute);
      setSelectedPlayer(null);
      setSelectedAttribute(null);
      setShowPlayerDropdown(false);
      setShowAttributeDropdown(false);
    }
  };

  const handleClose = () => {
    setSelectedPlayer(null);
    setSelectedAttribute(null);
    setShowPlayerDropdown(false);
    setShowAttributeDropdown(false);
    onClose();
  };

  return (
      <DropdownContainer style={{ zIndex: showPlayerDropdown ? 2000 : 1000 }}>
        <DropdownButton onPress={() => setShowPlayerDropdown(!showPlayerDropdown)}>
          <DropdownText>
            {selectedPlayer
                ? `${selectedPlayer.firstName} ${selectedPlayer.surname}`
                : 'Choose a player...'}
          </DropdownText>
          <DropdownArrow>{showPlayerDropdown ? '▲' : '▼'}</DropdownArrow>
        </DropdownButton>

        {showPlayerDropdown && (
            <DropdownList style={{ zIndex: 2001 }}>
              <FlatList
                  data={players}
                  keyExtractor={(item) => item.playerId}
                  showsVerticalScrollIndicator={true}
                  nestedScrollEnabled={true}
                  keyboardShouldPersistTaps="handled"
                  style={{ maxHeight: 200 }}
                  renderItem={({ item: player }) => (
                      <DropdownItem onPress={() => handlePlayerSelect(player)}>
                        <DropdownItemText>
                          {player.firstName} {player.surname}
                        </DropdownItemText>
                      </DropdownItem>
                  )}
              />
            </DropdownList>
        )}
      </DropdownContainer>
    // <Modal visible={visible} transparent animationType="fade" onRequestClose={handleClose}>
    //   <ModalContainer>
    //     <ModalContent>
    //       <ModalTitle>Assign Player to Training</ModalTitle>
    //
    //       <SectionTitle>Select Player:</SectionTitle>
    //       <DropdownContainer style={{ zIndex: showPlayerDropdown ? 2000 : 1000 }}>
    //         <DropdownButton onPress={() => setShowPlayerDropdown(!showPlayerDropdown)}>
    //           <DropdownText>
    //             {selectedPlayer
    //               ? `${selectedPlayer.firstName} ${selectedPlayer.surname}`
    //               : 'Choose a player...'}
    //           </DropdownText>
    //           <DropdownArrow>{showPlayerDropdown ? '▲' : '▼'}</DropdownArrow>
    //         </DropdownButton>
    //
    //         {showPlayerDropdown && (
    //           <DropdownList style={{ zIndex: 2001 }}>
    //             <FlatList
    //               data={players}
    //               keyExtractor={(item) => item.playerId}
    //               showsVerticalScrollIndicator={true}
    //               nestedScrollEnabled={true}
    //               keyboardShouldPersistTaps="handled"
    //               style={{ maxHeight: 200 }}
    //               renderItem={({ item: player }) => (
    //                 <DropdownItem onPress={() => handlePlayerSelect(player)}>
    //                   <DropdownItemText>
    //                     {player.firstName} {player.surname}
    //                   </DropdownItemText>
    //                 </DropdownItem>
    //               )}
    //             />
    //           </DropdownList>
    //         )}
    //       </DropdownContainer>
    //
    //       {selectedPlayer && (
    //         <AssistantMessage>
    //           <AssistantMessageText>
    //             {getAssistantMessage(selectedPlayer)}
    //           </AssistantMessageText>
    //         </AssistantMessage>
    //       )}
    //
    //       <SectionTitle>Select Training Attribute:</SectionTitle>
    //       <DropdownContainer style={{ zIndex: showAttributeDropdown ? 1500 : 1000 }}>
    //         <DropdownButton
    //           onPress={() => setShowAttributeDropdown(!showAttributeDropdown)}
    //           style={{ opacity: selectedPlayer ? 1 : 0.5 }}
    //           disabled={!selectedPlayer}
    //         >
    //           <DropdownText>
    //             {selectedAttribute
    //               ? attributes.find((attr) => attr.key === selectedAttribute)?.name
    //               : 'Choose an attribute...'}
    //           </DropdownText>
    //           <DropdownArrow>{showAttributeDropdown ? '▲' : '▼'}</DropdownArrow>
    //         </DropdownButton>
    //
    //         {showAttributeDropdown && selectedPlayer && (
    //           <DropdownList style={{ zIndex: 1501 }}>
    //             <FlatList
    //               data={attributes}
    //               keyExtractor={(item) => item.key}
    //               showsVerticalScrollIndicator={true}
    //               nestedScrollEnabled={true}
    //               keyboardShouldPersistTaps="handled"
    //               style={{ maxHeight: 200 }}
    //               renderItem={({ item: attr }) => (
    //                 <DropdownItem onPress={() => handleAttributeSelect(attr.key)}>
    //                   <DropdownItemText>
    //                     {attr.name} ({attr.category})
    //                   </DropdownItemText>
    //                 </DropdownItem>
    //               )}
    //             />
    //           </DropdownList>
    //         )}
    //       </DropdownContainer>
    //
    //       <ButtonContainer>
    //         <Button onPress={handleClose}>
    //           <ButtonText>Cancel</ButtonText>
    //         </Button>
    //         <Button
    //           onPress={handleAssign}
    //           style={{ opacity: selectedPlayer && selectedAttribute ? 1 : 0.5 }}
    //           disabled={!selectedPlayer || !selectedAttribute}
    //         >
    //           <ButtonText>Assign</ButtonText>
    //         </Button>
    //       </ButtonContainer>
    //     </ModalContent>
    //   </ModalContainer>
    // </Modal>
  );
};

export default TrainingModal;
