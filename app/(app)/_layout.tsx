import { Nunito_400Regular, Nunito_700Bold, useFonts } from '@expo-google-fonts/nunito';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as NavigationBar from 'expo-navigation-bar';
import { Redirect, Stack, usePathname } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Text } from 'react-native';
import styled from 'styled-components/native';
import { BannerAd, RewardAdProvider } from '../../src/components/Ads';
import { AuthProvider, useAuth } from '../../src/context/AuthContext';
import { DataCacheProvider } from '../../src/context/DataCacheContext';
import { MANAGER_ID_KEY, ManagerProvider } from '../../src/context/ManagerContext';
import { TeamInputMethodProvider } from '../../src/context/TeamInputMethodContext';
import { useNotificationScheduler } from '../../src/hooks/useNotificationScheduler';
import { logger } from '../../src/utils/logger';

function NotificationInitializer() {
  const { updateNotificationScheduling } = useNotificationScheduler();

  useEffect(() => {
    // Initialize notification scheduling when the app starts
    updateNotificationScheduling();
  }, [updateNotificationScheduling]);

  return null;
}

const ContentContainer = styled.View`
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
`;

export default function AppLayout() {
  const { isAuthenticated, isLoading } = useAuth();
  const [hasTeamAssigned, setHasTeamAssigned] = useState<boolean | null>(null);
  const pathname = usePathname();

  useEffect(() => {
    NavigationBar.setVisibilityAsync('hidden');
  }, []);

  const [fontsLoaded] = useFonts({
    Nunito: Nunito_400Regular,
    NunitoBold: Nunito_700Bold,
  });

  // Check if manager has a team assigned in local storage
  useEffect(() => {
    async function checkManagerAssignment() {
      // Check if manager ID exists in storage
      const managerId = await AsyncStorage.getItem(MANAGER_ID_KEY);
      logger.log('Manager ID:', managerId);
      setHasTeamAssigned(managerId !== null);
    }
    checkManagerAssignment();
  }, [pathname]);

  if (!fontsLoaded || isLoading) {
    logger.log('Loading fonts...');
    return <Text>Loading...</Text>;
  }

  if (!isAuthenticated) {
    return <Redirect href="/login" />;
  }

  if (hasTeamAssigned === null) {
    logger.log('Checking team assignment...');
    return <Text>Loading...</Text>;
  }

  // If the user doesn't have a team assigned and we're not already on the team assignment screen,
  // redirect to team assignment
  if (
    !hasTeamAssigned &&
    pathname !== '/team-assignment' &&
    pathname !== '/notification-settings' &&
    pathname !== '/manager-profile'
  ) {
    logger.log(pathname);
    return <Redirect href="/(app)/team-assignment" />;
  }

  return (
    <AuthProvider>
      <DataCacheProvider>
        <ManagerProvider>
          <RewardAdProvider>
            <TeamInputMethodProvider>
              <NotificationInitializer />
              <ContentContainer>
                <Stack
                  screenOptions={{
                    headerTitleStyle: {
                      fontFamily: 'NunitoBold',
                      fontSize: 20,
                    },
                    headerBackVisible: true,
                  }}
                  key="Authenticated Stack"
                />
                {/*<BannerAd />*/}
              </ContentContainer>
            </TeamInputMethodProvider>
          </RewardAdProvider>
        </ManagerProvider>
      </DataCacheProvider>
    </AuthProvider>
  );
}
